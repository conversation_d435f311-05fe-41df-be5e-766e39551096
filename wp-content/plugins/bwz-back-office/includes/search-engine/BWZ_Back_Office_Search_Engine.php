<?php

require_once __DIR__ . '/../../../../../vendor/autoload.php';

use Tribe\Project\Object_Meta\Member_Stream_Meta;
use Tribe\Project\Object_Meta\Service_Post_Meta;
use Tribe\Project\Object_Meta\Media_Meta;
use Tribe\Project\Object_Meta\Theme_Settings;
use Tribe\Project\Object_Meta\Post_Meta;
use Tribe\Project\Object_Meta\Tool_Post_Meta;
use Tribe\Project\Post_Types\Post\Post;
use Tribe\Project\Post_Types\Media\Media;
use Tribe\Project\Post_Types\Download\Download;
use Tribe\Project\Post_Types\Service_Post\Service_Post;
use Tribe\Project\Post_Types\Tool_Post\Tool_Post;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Taxonomies\Media_Type\Media_Type;
use Tribe\Project\Taxonomies\Service_Format\Service_Format;
use Tribe\Project\Taxonomies\Tool_Format\Tool_Format;
use Tribe\Project\Templates\Components\routes\single\Single_Download_Controller;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;
use Tribe\Project\Templates\Components\Traits\Handles_Post_Types;
use Tribe\Project\Theme\Config\Image_Sizes;

class BWZ_Back_Office_Search_Engine {
	use Handles_Post_Types;
	use Handles_MemberPress_Permissions;

	private string $site_abbreviation;
	private bool $log_enabled;

	const CRONJOB_HOOK_NAME = 'bwz_search_engine_collect_post_data';
	const SEARCH_TABLE_NAME = 'bawz_search';

	public function __construct() {
		$this->site_abbreviation = get_option( 'bawz_site_abbreviation' );
		$this->log_enabled       = defined( 'BWZ_SEARCH_LOG' ) && BWZ_SEARCH_LOG;
	}

	public function init(): void {
		add_action( 'wp', [ $this, 'bwz_back_office_search_engine_cronjob' ] );

		// Twice Daily
		add_action( self::CRONJOB_HOOK_NAME, function () {
			$this->bwz_back_office_search_engine_collect_data( false );
		} );

		// Weekly to prevent issues in case of a missed daily cronjob
		add_action( self::CRONJOB_HOOK_NAME . '_weekly_refresh', function () {
			$this->bwz_back_office_search_engine_collect_data( true );
		} );

		// Refresh table after table update, run once on each site
		add_action( self::CRONJOB_HOOK_NAME . '_refresh_table', function () {
			$this->bwz_back_office_search_engine_collect_data( true );
		} );

		add_filter( 'bawz_search_the_content', [ $this, 'bwz_back_office_search_engine_the_content' ] );

		$this->bwz_back_office_search_engine_check_db_version();
	}

	private function bwz_back_office_search_engine_check_db_version(): void {
		$current_version = get_option( 'bwz_search_db_version', '1.0' );
		$new_version     = '1.1';

		if ( ! current_user_can( 'manage_options' ) ) {
			return;
		}

		if ( version_compare( $current_version, $new_version, '<' ) ) {
			$execution = $this->bwz_back_office_search_engine_create_search_table();

			if ( ! empty( $execution ) ) {
				update_option( 'bwz_search_db_version', $new_version );

				// Run the cronjob to refresh the table data after the table is created
				if ( ! wp_next_scheduled( self::CRONJOB_HOOK_NAME . '_refresh_table' ) ) {
					wp_schedule_single_event( time() + 5 * MINUTE_IN_SECONDS, self::CRONJOB_HOOK_NAME . '_refresh_table' );
				}
			}
		}
	}

	private function bwz_back_office_search_engine_create_search_table(): array {
		require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );

		global $wpdb;

		$table_name      = self::SEARCH_TABLE_NAME;
		$charset_collate = $wpdb->get_charset_collate();

		$sql = <<<MYSQL
		DROP TABLE IF EXISTS `{$table_name}`;

		CREATE TABLE `{$table_name}` (
		    `id` bigint(20) PRIMARY KEY,
		    `blog_id` bigint(20) NOT NULL,
		    `site_name` varchar(255) NOT NULL,
		    `site_abbreviation` varchar(10) NOT NULL,
		    `post_id` bigint(20) NOT NULL,
		    `wpp_id` varchar(50) NOT NULL,
		    `post_type` varchar(20) NOT NULL,
		    `post_term` varchar(50) NULL,
		    `post_tool_format` varchar(50) NULL,
		    `post_title` text NOT NULL,
		    `post_excerpt` text NOT NULL,
		    `post_content` longtext NOT NULL,
		    `post_rendered_content` longtext NOT NULL,
		    `post_category` text NOT NULL,
		    `post_full_link` text NOT NULL,
		    `post_posted_at` datetime NOT NULL,
		    `post_updated_at` datetime NOT NULL,
		    `post_reading_time` varchar(30) NOT NULL,
		    `post_img_medium` varchar(255) NULL,
		    `post_img_large` varchar(255) NULL,
		    `author_name` text NOT NULL,
		    `author_full_link` text NOT NULL,
		    `multiauthor` json NULL,
		    `category_name` varchar(255) NULL,
		    `category_link` text NULL,
		    `is_members_only` tinyint(1) DEFAULT 0 NOT NULL,
		    `is_download_post` tinyint(1) DEFAULT 0 NOT NULL,
		    `files` varchar(100) NULL,
		    `language` char(2) DEFAULT 'en' NULL,
		    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP NOT NULL
		) $charset_collate;

		CREATE FULLTEXT INDEX `bawz_search_idx` ON `{$table_name}` (`post_title`, `post_excerpt`, `post_content`, `author_name`);
		CREATE FULLTEXT INDEX `bawz_search_post_author_name_idx` ON `{$table_name}` (`author_name`);
		CREATE FULLTEXT INDEX `bawz_search_post_content_idx` ON `{$table_name}` (`post_content`);
		CREATE FULLTEXT INDEX `bawz_search_post_excerpt_idx` ON `{$table_name}` (`post_excerpt`);
		CREATE FULLTEXT INDEX `bawz_search_post_title_idx` ON `{$table_name}` (`post_title`);
		CREATE INDEX `bawz_search_post_tool_format_IDX` ON `{$table_name}` (`post_tool_format`);
		CREATE INDEX `bawz_search_post_type_IDX` ON `{$table_name}` (`post_type`);
		CREATE INDEX `bawz_search_site_abbreviation_IDX` ON `{$table_name}` (`site_abbreviation`);
MYSQL;

		return dbDelta( $sql );
	}

	public function bwz_back_office_search_engine_cronjob(): void {
		if ( ! wp_next_scheduled( self::CRONJOB_HOOK_NAME ) ) {
            $blogs_offset_seconds = get_current_blog_id() * MINUTE_IN_SECONDS;
            $random_offset_seconds = rand( 1, 5 ) * MINUTE_IN_SECONDS;

			wp_schedule_event( time() + $blogs_offset_seconds + $random_offset_seconds, 'twicedaily', self::CRONJOB_HOOK_NAME );
		}

		if ( ! wp_next_scheduled( self::CRONJOB_HOOK_NAME . '_weekly_refresh' ) ) {
			wp_schedule_event( time(), 'weekly', self::CRONJOB_HOOK_NAME . '_weekly_refresh' );
		}
	}

	function bwz_back_office_search_engine_the_content( $content ): string {
		// Prevent rendering of specific blocks
		$blocks_to_remove = [
			'acf/contentloader',
			'acf/contentloop',
			'acf/contentloopcolumns',
			'acf/links',
			'acf/manualad',
		];

		// Parse the content into blocks
		$blocks = parse_blocks( $content );

		// Filter out the specific blocks
		$filtered_blocks = array_filter( $blocks, function ( $block ) use ( $blocks_to_remove ) {
			return ! in_array( $block['blockName'], $blocks_to_remove );
		} );

		// Rebuild the content from the filtered blocks
		$filtered_content = '';

		foreach ( $filtered_blocks as $block ) {
			if ( array_key_exists( 'blockName', $block ) && 'core/embed' === $block['blockName'] ) {
				$filtered_content .= apply_filters( 'the_content', render_block( $block ) );
			} else {
				$filtered_content .= do_shortcode( render_block( $block ) );
			}
		}

		return $filtered_content;
	}

	public function bwz_back_office_search_engine_collect_data( $fresh_table = false ): void {
		global $wpdb;

		if ( ! is_multisite() ) {
			return;
		}

		if ( empty( $this->site_abbreviation ) ) {
			return;
		}

		wpraiser_clear_opcache();

		if ( $fresh_table ) {
			$wpdb->delete( self::SEARCH_TABLE_NAME, [ 'site_abbreviation' => $this->site_abbreviation ] );

			$args = [
				'post_status' => 'publish',
				'post_type'   => [
					Post::NAME,
					Tool_Post::NAME,
					Service_Post::NAME,
					Media::NAME,
					Download::NAME,
					Member_Stream::NAME,
				],
				'nopaging'    => true,
			];
		} else {
			$args = [
				'post_status' => 'publish',
				'post_type'   => [
					Post::NAME,
					Tool_Post::NAME,
					Service_Post::NAME,
					Media::NAME,
					Download::NAME,
					Member_Stream::NAME,
				],
				'nopaging'    => true,
				'date_query'  => [
					[
						'column' => 'post_modified',
						'after'  => '3 days ago',
					],
				],
			];
		}

		$query = new WP_Query( $args );

		$this->write_to_log( [
			'query',
			$this->site_abbreviation,
			get_current_blog_id(),
			$query->found_posts,
		] );

		if ( $query->have_posts() ) {
			$blog_id           = get_current_blog_id();
			$site_name         = get_bloginfo( 'name' );
			$site_abbreviation = get_option( 'bawz_site_abbreviation' );

			while ( $query->have_posts() ) {
				$query->the_post();

				$post_id           = get_the_ID();
				$post_type         = get_post_type( $post_id );
				$post_title        = get_the_title();
				$post_excerpt      = get_the_excerpt();
				$post_content      = wp_strip_all_tags( get_the_content() );
				$post_raw_content  = apply_filters( 'bawz_search_the_content', get_the_content() );
				$post_full_link    = get_permalink();
				$post_posted_at    = get_the_date( 'Y-m-d H:i:s' );
				$post_updated_at   = get_the_modified_date( 'Y-m-d H:i:s' );
				$post_reading_time = trim( $this->get_duration( $post_id ) );
				$author_id         = get_post_field( 'post_author', $post_id );
				$author_name       = get_the_author_meta( 'display_name', $author_id );
				$author_full_link  = get_author_posts_url( $author_id );
				$multiauthor       = $this->get_multiauthor( get_post_type( $post_id ), $post_id );
				$image             = $this->get_image_args();
				$is_download_post  = $post_type === 'tribe_download' ? 1 : 0;
				$files             = $this->get_files();
				$is_members_only   = $this->is_members_only();
				$category          = get_the_category( $post_id );

				if ( function_exists( 'pll_get_post_language' ) ) {
					$language = pll_get_post_language( $post_id );
				} else {
					$language = 'en';
				}

				if ( isset( $category[0] ) ) {
					$category_name = $category[0]->cat_name;
					$category_link = get_term_link( $category[0]->term_id );
				}

				if ( $post_type === Media::NAME ) {
					$terms     = get_the_terms( $post_id, Media_Type::NAME );
					$post_term = isset( $terms[0] ) ? $terms[0]->slug : null;
				} else {
					$post_term = null;
				}

				if ( $post_type === Tool_Post::NAME ) {
					$terms            = get_the_terms( $post_id, Tool_Format::NAME );
					$post_tool_format = isset( $terms[0] ) ? $terms[0]->slug : null;
				} else {
					$post_tool_format = null;
				}

				if ( $post_type === Member_Stream::NAME ) {
					$event_host         = get_field( Member_Stream_Meta::HOST, $post_id );
					$event_description  = get_field( Member_Stream_Meta::DESCRIPTION, $post_id );
					$_event_description = preg_split( "/\r\n|\n|\r/", wp_strip_all_tags( $event_description ) );

					if ( ! empty( $event_host['display_name'] ) ) {
						$author_name = $event_host['display_name'];
					}

					$event_content = array_filter( $_event_description, function ( $k ) {
						return ! empty( $k );
					} );

					$post_content = implode( ' ', $event_content );
				}

				$data = [
					'blog_id'               => $blog_id,
					'site_name'             => $site_name,
					'site_abbreviation'     => $site_abbreviation,
					'post_id'               => $post_id,
					'wpp_id'                => $site_abbreviation . '_' . $post_id,
					'post_type'             => $post_type,
					'post_term'             => $post_term,
					'post_tool_format'      => $post_tool_format,
					'post_title'            => $post_title,
					'post_excerpt'          => $post_excerpt,
					'post_content'          => $post_content,
					'post_rendered_content' => $post_raw_content,
					'post_category'         => $category_name ?? '',
					'post_full_link'        => $post_full_link,
					'post_posted_at'        => $post_posted_at,
					'post_updated_at'       => $post_updated_at,
					'post_reading_time'     => $post_reading_time,
					'post_img_medium'       => array_key_exists( 'img_url_medium', $image ) ? $image['img_url_medium'] : '',
					'post_img_large'        => array_key_exists( 'img_url_large', $image ) ? $image['img_url_large'] : '',
					'author_name'           => $author_name,
					'author_full_link'      => $author_full_link,
					'multiauthor'           => $multiauthor,
					'category_name'         => $category_name ?? '',
					'category_link'         => $category_link ?? '',
					'is_members_only'       => $is_members_only,
					'is_download_post'      => $is_download_post,
					'files'                 => $files,
					'language'              => $language,
				];

				// Check if the post already exists in the table
				$exists = $wpdb->get_var( $wpdb->prepare( "SELECT COUNT(*) FROM " . self::SEARCH_TABLE_NAME . " WHERE blog_id = %d AND post_id = %d", $blog_id, $post_id ) );

				if ( $exists ) {
					$result = $wpdb->update( self::SEARCH_TABLE_NAME, $data, [
						'blog_id' => $blog_id,
						'post_id' => $post_id
					] );

					$this->write_to_log( [ 'update', $blog_id, $post_id, $result ], 'bawz_search-details.log' );
				} else {
					$result = $wpdb->insert( self::SEARCH_TABLE_NAME, $data );

					$this->write_to_log( [ 'insert', $blog_id, $post_id, $result ], 'bawz_search-details.log' );

					if ( $result === false ) {
						$this->write_to_log( [ 'insert_error', $blog_id, $post_id, $wpdb->last_error ], 'bawz_search-details.log' );
					}
				}
			}

			// Get the IDs of the published posts
			$published_post_ids = $wpdb->get_col( $wpdb->prepare( "SELECT ID FROM " . $wpdb->prefix . "posts WHERE post_status = 'publish'" ) );

			// Delete from the search table where the post_id is not in the list of published post IDs
			if ( ! empty( $published_post_ids ) ) {
				$placeholders = implode( ',', array_fill( 0, count( $published_post_ids ), '%d' ) );
				$result = $wpdb->query( $wpdb->prepare( "DELETE FROM " . self::SEARCH_TABLE_NAME . " WHERE blog_id = %d AND post_id NOT IN ($placeholders)", array_merge( [ $blog_id ], $published_post_ids ) ) );

				$this->write_to_log( [ 'deleted', $blog_id, 0, $result ], 'bawz_search-details.log' );
			}
		}

		wp_reset_postdata();
	}

	public function write_to_log( array $value, string $file = 'bawz_search.log' ): void {
		if ( ! $this->log_enabled ) {
			return;
		}

		$file = fopen( WP_CONTENT_DIR . '/' . $file, 'a' ) or die( "Unable to open file!" );

		fputcsv( $file, [
			time(),
			...$value
		] );

		fclose( $file );
	}

	public function get_image_args(): array {
		$image = get_post_thumbnail_id();

		if ( ! $image ) {
			$image = get_field( Theme_Settings::DEFAULT_FEATURED_IMAGE, 'options' );
		}

		$medium_attachment = wp_get_attachment_image_src( (int) $image, Image_Sizes::SQUARE_MEDIUM );
		$large_attachment  = wp_get_attachment_image_src( (int) $image, Image_Sizes::SQUARE_LARGE );

		$args = [];

		if ( $medium_attachment ) {
			$args['img_url_medium'] = $medium_attachment[0];
		}

		if ( $large_attachment ) {
			$args['img_url_large'] = $large_attachment[0];
		}

		return $args;
	}

	public function get_files(): string {
		$download_controller = new Single_Download_Controller;

		$files_types = array_column( $download_controller->get_files(), 'file_type' );

		$files = array_unique( $files_types );

		return implode( ',', $files );
	}

	public function get_multiauthor( $post_type, $post_id ): string|null {
		if ( $post_type == Download::NAME ) {
			return null;
		}

		$result                   = [];
		$add_reviewer_as_coauthor = '';
		$reviewers                = '';
		$multiauthors             = '';

		if ( $post_type == Post::NAME ) {
			$add_reviewer_as_coauthor = get_field( Post_Meta::REVIEWER_AS_COAUTHOR, $post_id );
			$reviewers                = $this->get_reviewers( $post_type );
			$multiauthors             = get_field( Post_Meta::MULTIAUTHOR, $post_id );
		} elseif ( $post_type == Tool_Post::NAME ) {
			$add_reviewer_as_coauthor = get_field( Tool_Post_Meta::REVIEWER_AS_COAUTHOR, $post_id );
			$reviewers                = $this->get_reviewers( $post_type );
			$multiauthors             = get_field( Tool_Post_Meta::MULTIAUTHOR, $post_id );
		} elseif ( $post_type == Service_Post::NAME ) {
			$add_reviewer_as_coauthor = get_field( Service_Post_Meta::REVIEWER_AS_COAUTHOR, $post_id );
			$reviewers                = $this->get_reviewers( $post_type );
			$multiauthors             = get_field( Service_Post_Meta::MULTIAUTHOR, $post_id );
		} elseif ( $post_type == Media::NAME ) {
			$multiauthors = get_field( Media_Meta::MULTIAUTHOR, $post_id );
		}

		if ( $add_reviewer_as_coauthor && ! empty( $reviewer ) ) {
			$result[] = [
				'author_name'      => get_the_author_meta( 'display_name', $reviewer['ID'] ),
				'author_full_link' => get_author_posts_url( $reviewer['ID'] ),
			];
		}

		if ( ! empty( $multiauthors ) ) {
			foreach ( $multiauthors as $author_id ) {
				$result[] = [
					'author_name'      => get_the_author_meta( 'display_name', $author_id ),
					'author_full_link' => get_author_posts_url( $author_id ),
				];
			}
		}

		return json_encode( $result ) ?? null;
	}

	protected function get_reviewers( string $post_type ): ?array {
		if ( $post_type == Post::NAME ) {
			$reviewer_contributor = get_field( Post_Meta::REVIEWER_CONTRIBUTOR, get_the_ID() );
		} elseif ( $post_type == Tool_Post::NAME ) {
			$reviewer_contributor = get_field( Tool_Post_Meta::REVIEWER_CONTRIBUTOR, get_the_ID() );
		} elseif ( $post_type == Service_Post::NAME ) {
			$reviewer_contributor = get_field( Service_Post_Meta::REVIEWER_CONTRIBUTOR, get_the_ID() );
		} else {
			$reviewer_contributor = null;
		}

		if ( ! $reviewer_contributor ) {
			return [];
		}

		foreach ( $reviewer_contributor as $entity ) {
			if ( $entity['entity_type'] === 'reviewer' ) {
				return $entity['users'];
			}
		}

		return [];
	}
}
