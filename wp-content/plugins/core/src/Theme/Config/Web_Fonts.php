<?php

namespace Tribe\Project\Theme\Config;

use Tribe\Project\Theme_Customizer\Customizer_Sections\Fonts;

/**
 * Web Fonts Configuration Class
 *
 * To add a variable font, include these additional properties in the font array:
 * - 'variable_font' => true
 * - 'font_axes' => 'ital,wght@0,100..900;1,100..900' (or custom axes like opsz, slnt, etc.)
 *
 * Example for a variable font:
 * self::FONT_NAME => [
 *     'label'           => 'Font Display Name',
 *     'url_param'       => 'Font+Name',
 *     'fallback'        => '"Font Name-fallback", fallback-family',
 *     'custom_fallback' => '@font-face {...}',
 *     'variable_font'   => true,
 *     'font_axes'       => 'ital,opsz,wght@0,9..144,100..900;1,9..144,100..900',
 * ],
 */
class Web_Fonts {
	public const  PROVIDER_TYPEKIT = 'typekit';
	public const  PROVIDER_GOOGLE  = 'google';
	public const  PROVIDER_CUSTOM  = 'custom';
	private const TYPEKIT_API      = 'https://use.typekit.net';

	public const  OPEN_SANS         = 'open_sans';
	public const  ARIAL             = 'arial';
	public const  POPPINS           = 'poppins';
	public const  ROBOTO            = 'roboto';
	public const  WORK_SANS         = 'work_sans';
	public const  PT_SERIF          = 'pt_serif';
	public const  SPACE_MONO        = 'space_mono';
	public const  PLAYFAIR_DISPLAY  = 'playfair_display';
	public const  OSWALD            = 'oswald';
	public const  MONTSERRAT        = 'montserrat';
	public const  LATO              = 'lato';
	public const  CHIVO             = 'chivo';
	public const  ARVO              = 'arvo';
	public const  RALEWAY           = 'raleway';
	public const  LIBRE_FRANKLIN    = 'libre_franklin';
	public const  LIBRE_BASKERVILLE = 'libre_baskerville';
	public const  LEXEND            = 'lexend';
	public const  LEXEND_DECA       = 'lexend_deca';
	public const  SOURCE_SANS       = 'source_sans';
	public const  CABIN             = 'cabin';
	public const  MERRIWEATHER      = 'merriweather';
	public const  OVERPASS          = 'overpass';
	public const  DARKER_GROTESQUE  = 'darker_grotesque';
	public const  NOTO_SANS         = 'noto_sans';
	public const  ESTEBAN           = 'esteban';
	public const  LORA              = 'lora';
	public const  HEEBO             = 'heebo';
	public const  IBM_PLEX_SERIF    = 'ibm_plex_serif';
	public const  IBM_PLEX_SANS     = 'ibm_plex_sans';
	public const  MULISH            = 'mulish';
	public const  DOMINE            = 'domine';
	public const  KAISEI_DECOL      = 'kaisei_decol';
	public const  SORA              = 'sora';
	public const  RUFINA            = 'rufina';
	public const  MANROPE           = 'manrope';
	public const  ROBOTO_SLAB       = 'roboto_slab';
	public const  SOURCE_SERIF      = 'source_serif';
	public const  BITTER            = 'bitter';
	public const  INTER             = 'inter';
	public const  FRAUNCES          = 'fraunces';

	/**
	 * @var array
	 */
	private array $fonts;

	public function __construct( array $fonts = [] ) {
		$this->fonts = $fonts;
	}

	/**
	 * List of available fonts for the system.
	 *
	 * @return \string[][]
	 */
	public static function get_google_fonts(): array {
		return [
			self::OPEN_SANS         => [
				'label'           => 'Open Sans',
				'url_param'       => 'Open+Sans',
				'fallback'        => '"Open Sans-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Open Sans-fallback";size-adjust: 105.40000000000003%;ascent-override: 106%;src: local("Arial");}',
			],
			self::ARIAL             => [
				'label'     => 'Arial',
				'url_param' => 'Arial',
				'fallback'  => 'sans-serif',
			],
			self::POPPINS           => [
				'label'           => 'Poppins',
				'url_param'       => 'Poppins',
				'fallback'        => '"Poppins-fallback", Geneva, Verdana, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Poppins-fallback";size-adjust: 110%;ascent-override: 102%;descent-override: 32%;src: local("Arial");}',
			],
			self::ROBOTO            => [
				'label'           => 'Roboto',
				'url_param'       => 'Roboto',
				'fallback'        => '"Roboto-fallback", Helvetica, Arial, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Roboto-fallback";size-adjust: 100.05%;ascent-override: 93%;src: local("Arial");}',
			],
			self::WORK_SANS         => [
				'label'           => 'Work Sans',
				'url_param'       => 'Work+Sans',
				'fallback'        => '"Work Sans-fallback", Geneva, Verdana, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Work Sans-fallback";size-adjust: 111.75%;ascent-override: 80%;src: local("Arial");}',
			],
			self::PT_SERIF          => [
				'label'           => 'PT Serif',
				'url_param'       => 'PT+Serif',
				'fallback'        => '"PT Serif-fallback", Georgia, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "PT Serif-fallback";size-adjust: 110.50000000000003%;ascent-override: 97%;src: local("Times New Roman");}',
			],
			self::SPACE_MONO        => [
				'label'           => 'Space Mono',
				'url_param'       => 'Space+Mono',
				'fallback'        => '"Space Mono-fallback", mono',
				'custom_fallback' => '@font-face {font-family: "Space Mono-fallback";size-adjust: 136.68999999999997%;ascent-override: 80%;src: local("Arial");}',
			],
			self::PLAYFAIR_DISPLAY  => [
				'label'           => 'Playfair Display',
				'url_param'       => 'Playfair+Display',
				'fallback'        => '"Playfair Display-fallback", Georgia, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "Playfair Display-fallback";size-adjust: 111.20%;ascent-override: 96%;src: local("Times New Roman");}',
			],
			self::OSWALD            => [
				'label'           => 'Oswald',
				'url_param'       => 'Oswald',
				'fallback'        => '"Oswald-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Oswald-fallback";size-adjust: 81.65000000000005%;ascent-override: 160%;src: local("Arial");}',
			],
			self::MONTSERRAT        => [
				'label'           => 'Montserrat',
				'url_param'       => 'Montserrat',
				'fallback'        => '"Montserrat-fallback", Geneva, Verdana, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Montserrat-fallback";size-adjust: 113.40%;ascent-override: 80%;src: local("Arial");}',
			],
			self::LATO              => [
				'label'           => 'Lato',
				'url_param'       => 'Lato',
				'fallback'        => '"Lato-fallback", "Trebuchet MS", Tahoma, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Lato-fallback";size-adjust: 97.40%;ascent-override: 100%;src: local("Arial");}',
			],
			self::CHIVO             => [
				'label'           => 'Chivo',
				'url_param'       => 'Chivo',
				'fallback'        => '"Chivo-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Chivo-fallback";size-adjust: 107.39999999999998%;ascent-override: 82%;src: local("Arial");}',
			],
			self::ARVO              => [
				'label'           => 'Arvo',
				'url_param'       => 'Arvo',
				'fallback'        => '"Arvo-fallback", serif',
				'custom_fallback' => '@font-face {font-family: "Arvo-fallback";size-adjust: 120.85%;ascent-override: 80%;src: local("Times New Roman");}',
			],
			self::RALEWAY           => [
				'label'           => 'Raleway',
				'url_param'       => 'Raleway',
				'fallback'        => '"Raleway-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Raleway-fallback";size-adjust: 104.05000000000008%;ascent-override: 93%;src: local("Arial");}',
			],
			self::LIBRE_FRANKLIN    => [
				'label'           => 'Libre Franklin',
				'url_param'       => 'Libre+Franklin',
				'fallback'        => '"Libre Franklin-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Libre Franklin-fallback";size-adjust: 104.30%;ascent-override: 93%;src: local("Arial");}',
			],
			self::LIBRE_BASKERVILLE => [
				'label'           => 'Libre Baskerville',
				'url_param'       => 'Libre+Baskerville',
				'fallback'        => '"Libre Baskerville-fallback", serif',
				'custom_fallback' => '@font-face {font-family: "Libre Baskerville-fallback";size-adjust: 127.29999999999998%;ascent-override: 65%;src: local("Times New Roman");}',
			],
			self::LEXEND            => [
				'label'           => 'Lexend',
				'url_param'       => 'Lexend',
				'fallback'        => '"Lexend-Fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Lexend-Fallback";size-adjust: 108%;ascent-override: 91%;src: local("Arial");}',
			],
			self::LEXEND_DECA       => [
				'label'           => 'Lexend Deca',
				'url_param'       => 'Lexend+Deca',
				'fallback'        => '"Lexend Deca-fallback", Geneva, Verdana, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Lexend Deca-fallback";size-adjust: 110.10%;ascent-override: 88%;src: local("Arial");}',
			],
			self::SOURCE_SANS       => [
				'label'           => 'Source Sans',
				'url_param'       => 'Source+Sans+3',
				'fallback'        => '"Source Sans-Fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Source Sans-Fallback";size-adjust: 93%;ascent-override: 107%;descent-override: 42%;src: local("Arial")}',
			],
			self::CABIN             => [
				'label'           => 'Cabin',
				'url_param'       => 'Cabin',
				'fallback'        => '"Cabin-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Cabin-fallback";size-adjust: 94.69999999999996%;ascent-override: 98%;src: local("Arial");}',
			],
			self::MERRIWEATHER      => [
				'label'           => 'Merriweather',
				'url_param'       => 'Merriweather',
				'fallback'  => '"Merriweather-fallback", Georgia, Verdana, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "Merriweather-fallback";size-adjust: 111%;ascent-override: 84%; line-gap-override: 7%;src: local("Georgia");}',
			],
			self::OVERPASS          => [
				'label'           => 'Overpass',
				'url_param'       => 'Overpass',
				'fallback'        => '"Overpass-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Overpass-fallback";size-adjust: 100.60000000000002%;ascent-override: 102%;src: local("Arial");}',
			],
			self::DARKER_GROTESQUE  => [
				'label'           => 'Darker Grotesque',
				'url_param'       => 'Darker+Grotesque',
				'fallback'        => '"Darker Grotesque-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Darker Grotesque-fallback";size-adjust: 81.40000000000003%;ascent-override: 140%;src: local("Arial");}',
			],
			self::NOTO_SANS         => [
				'label'           => 'Noto Sans',
				'url_param'       => 'Noto+Sans',
				'fallback'        => '"Noto Sans-fallback", "Trebuchet MS", Tahoma, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Noto Sans-fallback";size-adjust: 106.55%;ascent-override: 97%;src: local("Arial");}',
			],
			self::ESTEBAN           => [
				'label'           => 'Esteban',
				'url_param'       => 'Esteban',
				'fallback'        => '"Esteban-fallback", Georgia, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "Esteban-fallback";size-adjust: 109.88999999999994%;ascent-override: 90%;src: local("Times New Roman");}',
			],
			self::LORA              => [
				'label'           => 'Lora',
				'url_param'       => 'Lora',
				'fallback'        => '"Lora-fallback", Georgia, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "Lora-fallback";size-adjust: 115.65%;ascent-override: 81%;src: local("Times New Roman");}',
			],
			self::HEEBO             => [
				'label'           => 'Heebo',
				'url_param'       => 'Heebo',
				'fallback'        => '"Heebo-fallback", Helvetica, Arial, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Heebo-fallback";size-adjust: 100.30%;ascent-override: 120%;src: local("Arial");}',
			],
			self::IBM_PLEX_SERIF    => [
				'label'           => 'IBM Plex Serif',
				'url_param'       => 'IBM+Plex+Serif',
				'fallback'        => '"IBM Plex Serif-fallback", Georgia, Verdana, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "IBM Plex Serif-fallback";size-adjust: 105.5%;ascent-override: 109%;line-gap-override: 5%;src: local("Georgia");}',
			],
			self::IBM_PLEX_SANS     => [
				'label'           => 'IBM Plex Sans',
				'url_param'       => 'IBM+Plex+Sans',
				'fallback'        => '"IBM Plex Sans-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "IBM Plex Sans-fallback";size-adjust: 101.45%;ascent-override: 110%;src: local("Arial");}',
			],
			self::MULISH            => [
				'label'           => 'Mulish',
				'url_param'       => 'Mulish',
				'fallback'        => '"Mulish-fallback", "Trebuchet MS", Tahoma, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Mulish-fallback";size-adjust: 104.05%;ascent-override: 94%;src: local("Arial");}',
			],
			self::DOMINE            => [
				'label'           => 'Domine',
				'url_param'       => 'Domine',
				'fallback'        => '"Domine-fallback", Georgia, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "Domine-fallback";size-adjust: 119.79999999999995%;ascent-override: 70%;src: local("Times New Roman");}',
			],
			self::KAISEI_DECOL      => [
				'label'           => 'Kaisei Decol',
				'url_param'       => 'Kaisei+Decol',
				'fallback'        => '"Kaisei Decol-Fallback", Verdana, Georgia, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "Kaisei Decol-Fallback";size-adjust: 123%;ascent-override: 90%;src: local("Times New Roman");}',
			],
			self::SORA              => [
				'label'           => 'Sora',
				'url_param'       => 'Sora',
				'fallback'        => '"Sora-fallback", Tahoma, "Trebuchet MS", Geneva, Verdana, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Sora-fallback";size-adjust: 113.60%;ascent-override: 85%;src: local("Arial");}',
			],
			self::RUFINA            => [
				'label'           => 'Rufina',
				'url_param'       => 'Rufina',
				'fallback'        => '"Rufina-fallback", "Trebuchet MS", Georgia, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "Rufina-fallback";size-adjust: 115.15%;ascent-override: 81%;src: local("Times New Roman");}',
			],
			self::MANROPE           => [
				'label'           => 'Manrope',
				'url_param'       => 'Manrope',
				'fallback'        => '"Manrope-fallback", Helvetica, Arial, sans-serif',
				'custom_fallback' => '@font-face {font-family: "Manrope-fallback";size-adjust: 102.95%;ascent-override: 109%;src: local("Arial");}',
			],
			self::ROBOTO_SLAB       => [
				'label'           => 'Roboto Slab',
				'url_param'       => 'Roboto+Slab',
				'fallback'        => '"Roboto Slab-fallback", "Trebuchet MS", Georgia, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "Roboto Slab-fallback";size-adjust: 117.78999999999995%;ascent-override: 83%;src: local("Times New Roman");}',
			],
			self::SOURCE_SERIF      => [
				'label'           => 'Source Serif 4',
				'url_param'       => 'Source+Serif+4',
				'fallback'        => '"Source Serif 4-Fallback", serif',
				'custom_fallback' => '@font-face {font-family: "Source Serif 4-Fallback";size-adjust: 106%;ascent-override: 84%;src: local("Times New Roman");}',
			],
			self::BITTER            => [
				'label'           => 'Bitter',
				'url_param'       => 'Bitter',
				'fallback'        => '"Bitter-fallback", serif',
				'custom_fallback' => '@font-face {font-family: "Bitter-fallback";size-adjust: 114.49999999999997%;ascent-override: 80%;src: local("Times New Roman");}',
			],
			self::INTER             => [
				'label'           => 'Inter',
				'url_param'       => 'Inter',
				'fallback'        => '"Inter-fallback", sans-serif',
				'custom_fallback' => '@font-face {font-family: "Inter-fallback";size-adjust: 107.00%;ascent-override: 86%;src: local("Arial");}',
			],
			self::FRAUNCES          => [
				'label'           => 'Fraunces',
				'url_param'       => 'Fraunces',
				'fallback'        => '"Fraunces-fallback", Georgia, "Times New Roman", serif',
				'custom_fallback' => '@font-face {font-family: "Fraunces-fallback";size-adjust: 110%;ascent-override: 85%;src: local("Times New Roman");}',
				'variable_font'   => true,
				'font_axes'       => 'ital,opsz,wght@0,9..144,100..900;1,9..144,100..900',
			],
		];
	}

	/**
	 * Return a single Google front object from the array.
	 *
	 * @param $key
	 *
	 * @return array|string[]
	 */
	public static function get_single_google_font( $key ) {
		return self::get_google_fonts()[ $key ] ?: [];
	}

	/**
	 * Enqueue any required web fonts and custom fallbacks
	 *
	 * @action wp_enqueue_scripts
	 * @action login_enqueue_scripts
	 * @action enqueue_block_editor_assets
	 */
	public function enqueue_fonts(): void {
		foreach ( $this->get_custom_fallbacks() as $provider => $custom_fallback ) {
			$inline_handle = 'tribe-' . $provider . '-fallback';

			if ( empty( trim( (string) $custom_fallback ) ) ) {
				continue;
			}

			wp_register_style( $inline_handle, false );
			wp_enqueue_style( $inline_handle );
			wp_add_inline_style( $inline_handle, $custom_fallback );
		}

		foreach ( $this->get_font_urls() as $provider => $url ) {
			wp_enqueue_style( 'tribe-' . $provider, $url, [], null, 'all' );
		}
	}

	/**
	 * Unsupported Browser Page Fonts.
	 *
	 * @action tribe/unsupported_browser/head
	 */
	public function inject_unsupported_browser_fonts(): void {
		foreach ( $this->get_font_urls() as $url ) {
			printf( "<link rel='stylesheet' href='%s' type='text/css' media='all'>\n\t", esc_url( $url ) );
		}
	}

	/**
	 * TinyMCE Editor Fonts
	 *
	 * @action after_setup_theme
	 */
	public function add_tinymce_editor_fonts(): void {
		foreach ( $this->get_font_urls() as $url ) {
			add_editor_style( $url );
		}
	}

	/**
	 * Setup the font URLs array for use throughout.
	 */
	private function get_font_urls(): array {
		$urls = [];
		// Typekit
		if ( ! empty( $this->fonts[ self::PROVIDER_TYPEKIT ] ) ) {
			$urls[ self::PROVIDER_TYPEKIT ] = $this->get_typekit_url();
		}

		// Google
		if ( ! empty( $this->fonts[ self::PROVIDER_GOOGLE ] ) ) {
			$urls[ self::PROVIDER_GOOGLE ] = $this->get_google_url();
		}

		// Custom
		foreach ( $this->fonts[ self::PROVIDER_CUSTOM ] ?? [] as $index => $custom_url ) {
			$urls[ self::PROVIDER_CUSTOM . '-' . $index ] = $custom_url;
		}

		return $urls;
	}

	/**
	 * Setup the font Custom Fallbacks array for use throughout.
	 */
	private function get_custom_fallbacks(): array {
		$custom_fallbacks = [];

		// Google
		if ( ! empty( $this->fonts[ self::PROVIDER_GOOGLE ] ) ) {
			$primary_font   = get_theme_mod( Fonts::PRIMARY_FONT, self::POPPINS );
			$secondary_font = get_theme_mod( Fonts::SECONDARY_FONT, self::POPPINS );

			if ( $secondary_font === $primary_font ) {
				$custom_fallbacks[ self::PROVIDER_GOOGLE ] = $this->get_font_custom_fallback( $primary_font );

				return $custom_fallbacks;
			}

			$custom_fallbacks[ self::PROVIDER_GOOGLE ] = join( " ", array_map( function ( $font ) {
				return $this->get_font_custom_fallback( $font );
			}, [ $primary_font, $secondary_font ] ) );
		}

		return $custom_fallbacks;
	}

	/**
	 * @param mixed $font
	 *
	 * @return string
	 */
	private function get_font_custom_fallback( mixed $font ): string {
		$font_array = self::get_single_google_font( $font );

		if ( ! isset( $font_array['custom_fallback'] ) ) {
			return '';
		}

		return $font_array['custom_fallback'];
	}

	/**
	 * Returns a fully-qualified Google font URL.
	 *
	 * @return string
	 */
	private function get_google_url(): string {
		$primary_font   = get_theme_mod( Fonts::PRIMARY_FONT, self::POPPINS );
		$secondary_font = get_theme_mod( Fonts::SECONDARY_FONT, self::POPPINS );

		$families = sprintf( 'family=%s:%s',
			self::get_single_google_font( $primary_font )['url_param'],
			$this->get_font_parameters( $primary_font )
		);

		if ( $secondary_font !== $primary_font ) {
			$families .= sprintf( '&family=%s:%s',
				self::get_single_google_font( $secondary_font )['url_param'],
				$this->get_font_parameters( $secondary_font )
			);
		}

		$families = $this->get_subsets( $families );

		return sprintf(
			"https://fonts.googleapis.com/css2?%s&display=swap",
			$families,
		);
	}

	/**
	 * Get font parameters for Google Fonts URL based on font type.
	 *
	 * @param string $font_key
	 * @return string
	 */
	private function get_font_parameters( string $font_key ): string {
		$font_config = self::get_single_google_font( $font_key );

		// Check if this is a variable font with custom axes
		if ( ! empty( $font_config['variable_font'] ) && ! empty( $font_config['font_axes'] ) ) {
			return $font_config['font_axes'];
		}

		// Default parameters for standard fonts
		return 'ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700';
	}

	/**
	 * Returns a fully-qualified Typekit font kit URL from the Project ID value.
	 *
	 * @return string
	 */
	private function get_typekit_url(): string {
		if ( empty( $this->fonts[ self::PROVIDER_TYPEKIT ] ) ) {
			return '';
		}

		return sprintf( '%s%s.css', trailingslashit( self::TYPEKIT_API ), $this->fonts[ self::PROVIDER_TYPEKIT ] );
	}

	/**
	 * @param string $url
	 *
	 * @return string
	 */
	public function get_subsets( string $url ): string {
		$letters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$lower_letters = 'abcdefghijklmnopqrstuvwxyz';
		$numbers       = '**********';
		$symbols       = "+!\"#$%&'()*+,-—–-…./:;<=>?@[\]^_`{|}~ÁÀÄÂÉÈËÊÍÌÏÎÓÒÖÔÚÙÜÛÑÇáàäâéèëêíìïîóòöôúùüûñç«»°ºª´`ˆ¨¸";

		$subsets = $letters . $lower_letters . $numbers . $symbols;

		if ( function_exists( 'pll_the_languages' ) ) {
			$languages = pll_the_languages( [
				'raw' => true,
			] );

			if ( isset( $languages['fr'] ) ) {
				$subsets .= 'ÆŒŸæœÿ';
			}

			if ( isset( $languages['de'] ) ) {
				$subsets .= 'ÄÖÜßäöüß';
			}

			if ( isset( $languages['es'] ) ) {
				$subsets .= '¡¿';
			}
		}

		$url .= '&text=' . urlencode( $subsets );

		return $url;
	}
}
